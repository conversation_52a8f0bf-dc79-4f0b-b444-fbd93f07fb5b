[project]
name = "dts-fb-activities"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "DocSJ1980", email = "<EMAIL>" }
]
requires-python = ">=3.13"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "fastapi>=0.115.13",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pydantic-settings>=2.10.0",
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "uvicorn[standard]>=0.34.3",
]

[project.scripts]
dts-fb-activities = "dts_fb_activities:main"

[tool.hatch.envs.default.scripts]
start = "python -m dts_fb_activities.app"
dev = "uvicorn dts_fb_activities.app:app --reload --host 0.0.0.0 --port 8000"
prod = "uvicorn dts_fb_activities.app:app --host 0.0.0.0 --port 8000"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
