"""Main FastAPI application."""

from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import sys
from contextlib import asynccontextmanager

from .api.endpoints import router
from .core.config import settings
from .models.schemas import ErrorResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting DTS FB Activities API")
    logger.info(f"Version: {settings.app_version}")
    logger.info(f"Debug mode: {settings.debug}")
    
    # Validate configuration
    try:
        from .services.auth import auth_service
        from .services.data_access import data_service
        from .services.cache import cache_service
        logger.info("Configuration validated successfully")
        logger.info("Cache service initialized")
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        # Don't raise here to allow the app to start for health checks
    
    yield
    
    # Shutdown
    logger.info("Shutting down DTS FB Activities API")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="API for DTS FB Activities - Dengue Surveillance Data",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            detail="An unexpected error occurred. Please try again later."
        ).dict()
    )


# Include API router
app.include_router(router, prefix="/api/v1", tags=["DTS FB Activities"])


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "DTS FB Activities API",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/api/v1/health"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "dts_fb_activities.app:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
